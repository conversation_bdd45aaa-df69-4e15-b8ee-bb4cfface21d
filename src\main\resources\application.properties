# ==================== RENDER.COM CLOUD DEPLOYMENT CONFIGURATION ====================
# Configuration for deploying Sales Management Backend on Render.com
# Uses Aiven MySQL database with environment variables for secure cloud deployment

spring.application.name=SalesManagementBackend

# MySQL Cloud Database Configuration for Render.com
# Set DATABASE_URL environment variable with your actual database connection string
spring.datasource.url=${DATABASE_URL}
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}

# Production Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=${DB_POOL_MAX_SIZE:15}
spring.datasource.hikari.minimum-idle=${DB_POOL_MIN_IDLE:3}
spring.datasource.hikari.idle-timeout=${DB_POOL_IDLE_TIMEOUT:300000}
spring.datasource.hikari.connection-timeout=${DB_POOL_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.max-lifetime=${DB_POOL_MAX_LIFETIME:1200000}
spring.datasource.hikari.leak-detection-threshold=${DB_POOL_LEAK_DETECTION:60000}
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.validation-timeout=5000

# SSL Configuration for Aiven MySQL
spring.datasource.hikari.data-source-properties.useSSL=true
spring.datasource.hikari.data-source-properties.requireSSL=true
spring.datasource.hikari.data-source-properties.verifyServerCertificate=false

# JPA/Hibernate Configuration - Fixed schema filter issue
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=${DB_DDL_AUTO:update}
spring.jpa.show-sql=${DB_SHOW_SQL:false}
spring.jpa.properties.hibernate.format_sql=${DB_FORMAT_SQL:false}
spring.jpa.properties.hibernate.use_sql_comments=false
spring.jpa.properties.hibernate.globally_quoted_identifiers=true
spring.jpa.properties.hibernate.id.new_generator_mappings=true
spring.jpa.properties.hibernate.hbm2ddl.create_namespaces=true
spring.jpa.properties.hibernate.hbm2ddl.halt_on_error=false
spring.jpa.defer-datasource-initialization=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.open-in-view=false

# Schema filter provider removed - let Hibernate use its default implementation
# spring.jpa.properties.hibernate.hbm2ddl.schema_filter_provider= (removed - was causing empty string error)

# Server Configuration for Render.com
server.port=${PORT:8080}
server.servlet.context-path=${CONTEXT_PATH:/}
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024
server.forward-headers-strategy=framework

# Production Logging Configuration
logging.level.com.hamza.salesmanagementbackend=${LOG_LEVEL:INFO}
logging.level.org.springframework.web.servlet.DispatcherServlet=WARN
logging.level.org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.level.org.springframework.security=WARN

# JWT Configuration
# Set JWT_SECRET environment variable with your actual JWT secret key
jwt.secret=${JWT_SECRET}
jwt.expiration=${JWT_EXPIRATION:86400000}

# CORS Configuration
cors.allowed-origins=${CORS_ALLOWED_ORIGINS:*}
cors.allowed-methods=${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE,OPTIONS}
cors.allowed-headers=${CORS_ALLOWED_HEADERS:*}
cors.max-age=${CORS_MAX_AGE:3600}

# Static Resources Configuration
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.add-mappings=true
spring.web.resources.cache.cachecontrol.max-age=31536000

# Actuator Configuration
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=when-authorized
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.ping.enabled=true

# Update System Configuration
app.updates.storage-path=${UPDATE_STORAGE_PATH:/tmp/versions}
app.updates.max-file-size=${UPDATE_MAX_FILE_SIZE:524288000}
app.updates.allowed-extensions=${UPDATE_ALLOWED_EXTENSIONS:jar}
app.updates.enable-resumable-downloads=${UPDATE_ENABLE_RESUMABLE:true}
app.updates.cleanup-orphaned-files=${UPDATE_CLEANUP_ORPHANED:true}
app.updates.websocket.heartbeat-interval=${UPDATE_HEARTBEAT_INTERVAL:30000}
app.updates.websocket.connection-timeout=${UPDATE_CONNECTION_TIMEOUT:300000}
app.updates.security.admin-role=${UPDATE_ADMIN_ROLE:ADMIN}
app.updates.security.rate-limit=${UPDATE_RATE_LIMIT:10}

# JAR File Validation Configuration
app.updates.jar-validation.strict-mime-type=${UPDATE_JAR_STRICT_MIME:true}
app.updates.jar-validation.require-manifest=${UPDATE_JAR_REQUIRE_MANIFEST:false}
app.updates.jar-validation.max-entries=${UPDATE_JAR_MAX_ENTRIES:10000}
app.updates.jar-validation.max-manifest-size=${UPDATE_JAR_MAX_MANIFEST_SIZE:65536}

# File Upload Configuration
spring.servlet.multipart.max-file-size=${MAX_FILE_SIZE:500MB}
spring.servlet.multipart.max-request-size=${MAX_REQUEST_SIZE:500MB}

# Production Security Settings
server.error.include-message=never
server.error.include-binding-errors=never
server.error.include-stacktrace=never
server.error.include-exception=false

# Performance Optimizations
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
spring.jpa.properties.hibernate.cache.use_query_cache=false
spring.jpa.properties.hibernate.generate_statistics=false